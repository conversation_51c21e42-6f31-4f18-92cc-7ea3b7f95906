import { faArrowRight } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useEffect, useRef, useState } from "react";
import { Link, useLoaderData, useLocation, useNavigate, useRouteLoaderData } from "react-router-dom";
import AbunButton from "../../components/AbunButton/AbunButton";
import CalculatorLoadingScreen from "../../components/CalculatorLoadingScreen/CalculatorLoadingScreen";
import ErrorAlert from "../../components/ErrorAlert/ErrorAlert";
import Icon from "../../components/Icon/Icon";
import SuccessAlert from "../../components/SuccessAlert/SuccessAlert";
import { withAdminAndProductionCheck } from "../../utils/adminAndProductionCheck";
import {
    checkJobStatus,
    generateAICalculatorMutation,
    getAICalculatorDataQuery,
    getCalculatorScriptTag,
    modifyAICalculatorMutation,
} from "../../utils/api";
import { BasePageData } from "../Base/Base";
import { PageData } from "../KeywordsResearchV2/KeywordResearch";
import { pageURL } from '../routes';
import './AICalculatorGenerator.min.css';
import AICalculatorTable from "./AICalculatorTable";

type AICalculatorVersion = {
    id: number;
    version_name: string;
    html_code: string;
    created_on: string;
};

// Browser Mockup Component
interface BrowserMockupProps {
    websiteDomain: string;
    calculatorTitle: string;
    children: React.ReactNode;
    isLoading?: boolean;
}

const BrowserMockup: React.FC<BrowserMockupProps> = ({ websiteDomain, calculatorTitle, children, isLoading = false }) => {
    // Create a URL-friendly slug from the calculator title
    const createSlug = (title: string) => {
        if (!title || title.trim() === '') {
            return 'calculator';
        }
        return title
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
            .replace(/\s+/g, '-') // Replace spaces with hyphens
            .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
            .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
            .trim() || 'calculator'; // Fallback if empty after processing
    };

    const slug = createSlug(calculatorTitle);
    const mockUrl = `https://${websiteDomain}/${slug}`;

    return (
        <div className="browser-mockup">
            <div className="browser-header">
                <div className="browser-controls">
                    <div className="browser-control close"></div>
                    <div className="browser-control minimize"></div>
                    <div className="browser-control maximize"></div>
                </div>
                <div className="browser-address-bar">
                    <span className="browser-url">{mockUrl}</span>
                </div>
            </div>
            <div className="browser-notification-bar">
                <div className="notification-content">
                    <span className="notification-text">Your Existing Website NavBar</span>
                </div>
            </div>
            <div className="browser-content" style={{ position: 'relative' }}>
                {children}
                {isLoading && (
                    <div className="browser-loading-overlay">
                        <div className="browser-loading-content">
                            <div className="browser-loading-spinner"></div>
                            <p className="browser-loading-text">Updating calculator...</p>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

interface LocationState {
    calculatorId?: string;
    calculatorType?: string;
    userModifications?: string[];
    isGenerating?: boolean;
    jobId?: string;
    activeAIGeneratedTab?: string;
}


function AICalculatorGenerator() {
    // --------------------------- HOOKS ---------------------------
    const location = useLocation();
    const navigate = useNavigate();
    const state = location.state as LocationState;

    // --------------------------- STATES ---------------------------
    const [calculatorId, setCalculatorId] = useState<string | null>(state?.calculatorId || null);
    const [calculatorType, setCalculatorType] = useState<string>(state?.calculatorType || "");
    const [calculatorHTML, setCalculatorHTML] = useState<string>("");
    const [userInput, setUserInput] = useState<string>("");

    const [selectedVersion, setSelectedVersion] = useState(0);
    const [isChecked, setIsChecked] = useState(true);
    const [embedCode, setEmbedCode] = useState({ scriptTag: '' });
    const [generatingEmbedCode, setGeneratingEmbedCode] = useState(false);
    const [calcDescription, setCalcDescription] = useState<string>("");
    const [versions, setVersions] = useState<Array<AICalculatorVersion>>([]);
    const [jobId, setJobId] = useState<string>("");
    const [isGenerating, setIsGenerating] = useState<boolean>(false);
    const [showLoadingScreen, setShowLoadingScreen] = useState<boolean>(false);
    const [isModifying, setIsModifying] = useState<boolean>(false);
    const [activeAIGeneratedTab, setActiveAIGeneratedTab] = useState("ai-calculator");

    // Collapsible sections state
    const [expandedSections, setExpandedSections] = useState({
        update: true,      // Update Calculator is open by default
        versions: false,   // Versions is closed by default
        embed: false       // Get Embed Code is closed by default
    });

    // --------------------------- REFS ---------------------------
    const errorAlertRef = useRef<any>(null);
    const successAlertRef = useRef<any>(null);
    const previewContainerRef = useRef<HTMLDivElement>(null);

    // --------------------------- PAGE DATA ---------------------------
    const pageData = useLoaderData() as PageData;
    const basePageData: BasePageData = useRouteLoaderData("base") as BasePageData;

    // --------------------------- UTILITY FUNCTIONS ---------------------------
    const getJobStorageKey = (calcId: string) => `calculator_job_${calcId}`;

    const saveJobToStorage = (calcId: string, jobId: string, type: 'generation' | 'modification') => {
        if (calcId) {
            localStorage.setItem(getJobStorageKey(calcId), JSON.stringify({ jobId, type, timestamp: Date.now() }));
        }
    };

    const getJobFromStorage = (calcId: string) => {
        if (!calcId) return null;
        const stored = localStorage.getItem(getJobStorageKey(calcId));
        return stored ? JSON.parse(stored) : null;
    };

    const clearJobFromStorage = (calcId: string) => {
        if (calcId) {
            localStorage.removeItem(getJobStorageKey(calcId));
        }
    };

    // --------------------------- QUERIES ---------------------------
    // Query for fetching calculator data
    const {
        isLoading: isLoadingCalculator,
        refetch: refetchCalculatorData
    } = useQuery({
        ...getAICalculatorDataQuery(calculatorId || ''),
        enabled: !!calculatorId,
        onSuccess: (response: any) => {
            if (response.data) {
                const { calculator_id, calc_type, code, script_tag, versions } = response.data.calculator_data;
                // Set calculator details
                setCalculatorId(calculator_id);
                setCalculatorType(calc_type);
                setCalculatorHTML(code);
                setEmbedCode({ scriptTag: script_tag });

                // Set versions if available
                if (versions && versions.length > 0) {
                    setVersions(versions);
                    // Set the latest version as selected by default
                    setSelectedVersion(versions[0].id);
                }
            }
        },
        onError: () => {
            errorAlertRef.current?.show("Failed to load calculator data");
        }
    });

    // Job status polling query
    const { data: jobStatusData } = useQuery({
        ...checkJobStatus(jobId),
        enabled: !!jobId && (isGenerating || isModifying),
        refetchInterval: 3000, // Poll every 3 seconds
        onSuccess: (response: any) => {
            if (response?.data?.status === "completed") {
                // Job completed successfully
                const wasModifying = isModifying;

                // Clear states
                setIsGenerating(false);
                setIsModifying(false);
                setShowLoadingScreen(false);
                setJobId("");

                // Clear from localStorage
                if (calculatorId) {
                    clearJobFromStorage(calculatorId);
                }

                // Refetch calculator data to get the updated content
                if (calculatorId) {
                    refetchCalculatorData().then(() => {
                        const message = wasModifying
                            ? "Calculator updated successfully!"
                            : "Calculator generated successfully!";
                        successAlertRef.current?.show(message);
                        setTimeout(() => {
                            successAlertRef.current?.close();
                        }, 3000);
                    });
                }
            } else if (response?.data?.status === "failed") {
                // Job failed
                const wasModifying = isModifying;

                setIsGenerating(false);
                setIsModifying(false);
                setShowLoadingScreen(false);
                setJobId("");

                // Clear from localStorage
                if (calculatorId) {
                    clearJobFromStorage(calculatorId);
                }

                const errorMessage = wasModifying
                    ? "Calculator modification failed. Please try again."
                    : "Calculator generation failed. Please try again.";
                errorAlertRef.current?.show(errorMessage);
            }
        },
        onError: () => {
            setIsGenerating(false);
            setIsModifying(false);
            setShowLoadingScreen(false);
            setJobId("");

            // Clear from localStorage
            if (calculatorId) {
                clearJobFromStorage(calculatorId);
            }

            errorAlertRef.current?.show("Failed to check job status. Please try again.");
        }
    });

    // -------------------------- MUTATIONS ---------------------------
    // Mutation for generating a new calculator
    const generateAICalculatorMut = useMutation(generateAICalculatorMutation);

    // Mutation for modifying an existing calculator
    const modifyAICalculatorMut = useMutation(modifyAICalculatorMutation);

    // --------------------------- EFFECTS ---------------------------
    // Update preview when HTML changes and execute JavaScript
    useEffect(() => {
        if (previewContainerRef.current && calculatorHTML) {
            // Create an iframe
            const iframe = document.createElement('iframe');
            iframe.style.width = '100%';
            iframe.style.height = '100%';
            iframe.style.border = 'none';

            // Set the HTML content of the iframe
            iframe.srcdoc = calculatorHTML;

            // Clear previous content and append the iframe
            previewContainerRef.current.innerHTML = "";
            previewContainerRef.current.appendChild(iframe);
        }
    }, [calculatorHTML]);

    // Handle initial state from navigation (when coming from generation request)
    useEffect(() => {
        if (state?.isGenerating && state?.jobId && !calculatorHTML) {
            setJobId(state.jobId);
            setIsGenerating(true);
            setShowLoadingScreen(true);
            setCalculatorId(state.calculatorId || null);
            setCalculatorType(state.calculatorType || "");
        }
    }, [state, calculatorHTML]);

    // Check for existing jobs in localStorage on component mount
    useEffect(() => {
        if (calculatorId && !jobId) {
            const storedJob = getJobFromStorage(calculatorId);
            if (storedJob && storedJob.jobId) {
                // Check if the job is not too old (24 hours max)
                const isJobTooOld = Date.now() - storedJob.timestamp > 24 * 60 * 60 * 1000;

                if (!isJobTooOld) {
                    setJobId(storedJob.jobId);
                    if (storedJob.type === 'generation') {
                        setIsGenerating(true);
                        setShowLoadingScreen(true);
                    } else if (storedJob.type === 'modification') {
                        setIsModifying(true);
                    }
                } else {
                    // Clear old job from storage
                    clearJobFromStorage(calculatorId);
                }
            }
        }
    }, [calculatorId, jobId]);

    // Auto-hide loading screen if we have calculator data
    useEffect(() => {
        if (calculatorHTML && showLoadingScreen) {
            setShowLoadingScreen(false);
            setIsGenerating(false);
            setJobId("");
        }
    }, [calculatorHTML, showLoadingScreen]);

    useEffect(() => {
        if (state?.calculatorId && !calculatorHTML) {
            setCalculatorId(state.calculatorId);
            setCalculatorType(state.calculatorType || "");
        }
    }, [state, calculatorHTML]);

    useEffect(() => {
        if (location.state?.activeAIGeneratedTab) {
            setCalculatorId(null);
            setCalculatorHTML("");
        }
    }, [location.state]);

    // Safety timeout to prevent infinite loading (5 minutes max)
    useEffect(() => {
        let timeoutId: NodeJS.Timeout;

        if (isGenerating && showLoadingScreen) {
            timeoutId = setTimeout(() => {
                setIsGenerating(false);
                setShowLoadingScreen(false);
                setJobId("");
                errorAlertRef.current?.show("Calculator generation is taking longer than expected. Please try again or check your calculators list.");
            }, 5 * 60 * 1000); // 5 minutes
        }

        return () => {
            if (timeoutId) {
                clearTimeout(timeoutId);
            }
        };
    }, [isGenerating, showLoadingScreen]);

    // --------------------------- HANDLERS ---------------------------
    const toggleSection = (section: 'update' | 'versions' | 'embed') => {
        setExpandedSections(prev => ({
            ...prev,
            [section]: !prev[section]
        }));
    };

    const generateCalculator = () => {
        errorAlertRef.current?.close();
        successAlertRef.current?.close();

        if (!calculatorType || !calculatorType.trim()) {
            errorAlertRef.current?.show("Please enter a calculator type");
            return;
        }

        generateAICalculatorMut.mutate(
            { calc_type: calculatorType, calc_description: calcDescription },
            {
                onSuccess: (response) => {
                    const data = response.data;

                    if (data?.status === "submitted") {
                        // New flow: Job submitted successfully
                        setCalculatorId(data.calculator_id);
                        setJobId(data.job_id);
                        setIsGenerating(true);
                        setShowLoadingScreen(true);

                        // Save job to localStorage
                        saveJobToStorage(data.calculator_id, data.job_id, 'generation');

                        // Only navigate if we're not already on the calculator page
                        // This prevents navigation loops
                        if (!state?.calculatorId || state.calculatorId !== data.calculator_id) {
                            navigate("/ai-calculator-generator", {
                                state: {
                                    calculatorId: data.calculator_id,
                                    calculatorType: data.calc_type || calculatorType,
                                    isGenerating: true,
                                    jobId: data.job_id
                                },
                                replace: true // Use replace to avoid back button issues
                            });
                        }
                    } else if (data?.status === "rejected") {
                        // Handle limit reached or other rejections
                        if (data.reason === "max_calculator_limit_reached") {
                            errorAlertRef.current?.show(
                                `You have reached your monthly calculator limit of ${data.limit}. Upgrade your plan for more calculators.`
                            );
                        } else {
                            errorAlertRef.current?.show(data.message || "Request was rejected. Please try again.");
                        }
                    } else if (data?.status === "success") {
                        // Legacy flow: Immediate success (backward compatibility)
                        setCalculatorId(data.calculator_id);
                        setCalculatorHTML(data.html_content);
                        setCalculatorType(data.calc_type);
                        successAlertRef.current?.show("Calculator generated successfully!");
                        setTimeout(() => {
                            successAlertRef.current?.close();
                        }, 3000);
                    }
                },
                onError: (error: any) => {
                    // Handle 500 errors and other failures
                    const errorMessage = error?.response?.data?.message || "Failed to generate calculator. Please try again.";
                    errorAlertRef.current?.show(errorMessage);
                }
            }
        );
    };

    const requestModification = () => {
        errorAlertRef.current?.close();
        successAlertRef.current?.close();

        if ((!userInput || !userInput.trim()) || !calculatorId) {
            return;
        }

        const modificationText = userInput;
        setUserInput("");

        modifyAICalculatorMut.mutate(
            { calculator_id: calculatorId, modifications: modificationText },
            {
                onSuccess: (response) => {
                    const data = response.data;

                    if (data?.status === "submitted") {
                        setJobId(data.job_id);
                        setIsModifying(true);

                        // Save job to localStorage
                        if (calculatorId) {
                            saveJobToStorage(calculatorId, data.job_id, 'modification');
                        }

                        successAlertRef.current?.show("Calculator modification submitted. Please wait while we update your calculator.");
                        setTimeout(() => {
                            successAlertRef.current?.close();
                        }, 3000);
                    } else {
                        const errorMessage = response.data?.message || "Failed to modify calculator. Please try again.";
                        errorAlertRef.current?.show(errorMessage);
                        setTimeout(() => {
                            errorAlertRef.current?.close();
                        }, 3000);
                    }
                },
                onError: (error: any) => {
                    // Handle 500 errors and other failures
                    const errorMessage = error?.response?.data?.message || "Failed to modify calculator. Please try again.";
                    errorAlertRef.current?.show(errorMessage);
                }
            }
        );
    };

    const copyToClipboard = (text: string) => {
        navigator.clipboard.writeText(text).then(() => {
            successAlertRef.current?.show("Copied to clipboard!");
            setTimeout(() => {
                successAlertRef.current?.close();
            }, 3000);
        }).catch(() => {
            errorAlertRef.current?.show("Failed to copy to clipboard.");
        });
    };

    const backToList = () => {
        // navigate("/ai-calculator-generator");
        // navigate("/create-article");
        navigate("/ai-calculator-generator", {
            state: {
                activeAIGeneratedTab: "projects"
            }
        });
    };



    const handleGenerateEmbedCode = () => {
        if (!calculatorId) {
            errorAlertRef.current?.show("Calculator ID is missing. Please generate a calculator first.");
            setTimeout(() => {
                errorAlertRef.current?.close();
            }, 3000);
            return;
        }

        setGeneratingEmbedCode(true);
        const queryParams = { calculator_id: calculatorId };
        getCalculatorScriptTag(queryParams).then(response => {
            if (response.success) {
                setEmbedCode({ scriptTag: response.script_tag });
                successAlertRef.current?.show("Embed code generated successfully. Please add the following tags to your site.");
            } else {
                if (response.message) {
                    errorAlertRef.current?.show(response.message);
                } else {
                    errorAlertRef.current?.show("Failed to generate embed code. Please try again.");
                }
            }
        }).finally(() => {
            setGeneratingEmbedCode(false);
            setTimeout(() => {
                successAlertRef.current?.close();
                errorAlertRef.current?.close();
            }, 3000);
        });
    };



    const handleVersionChange = (versionId: number) => {
        setSelectedVersion(versionId);
        const selectedVersionData = versions.find(v => v.id === versionId);
        if (selectedVersionData) {
            setCalculatorHTML(selectedVersionData.html_code);
        }
    };

    // Determine if any mutation is loading
    const isLoading = (state?.calculatorId && isLoadingCalculator) ||
        generateAICalculatorMut.isLoading ||
        modifyAICalculatorMut.isLoading ||
        isModifying;

    // --------------------------- RENDER ---------------------------
    // Show loading screen if calculator is being generated
    if (showLoadingScreen && isGenerating) {
        return <CalculatorLoadingScreen calculatorType={calculatorType} />;
    }

    return (
        <>
            <div className="ai-calculator-generator-container">
                {/* <div className="ai-calculator-generator-header">
                    <button className="ai-calculator-back-button" onClick={backToList}>
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 12H5" stroke="#000000" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                            <path d="M12 19L5 12L12 5" stroke="#000000" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        </svg>
                    </button>
                </div> */}

                <div className="ai-calculator-generator-content">
                    {!calculatorHTML ? (
                        <>
                            <div className="mb-5 is-flex is-justify-content-space-between is-flex-wrap-wrap">
                                <div className="ai-calculator-header">
                                    <h2>AI Calculator Generator</h2>
                                    <p className={"has-text-dark"}>
                                        Create custom calculators for your website by simply describing what you need. <br />
                                        Great for SEO or helping users solve real problems on your site. <br />
                                        No code or formulas needed. Instantly embeddable on any site.
                                    </p>
                                </div>
                                <span className="is-block mt-2">
                                    {pageData.ai_calculator_remaining} AI Calculator Widgets remaining. <Link to={pageURL["subscriptionCredit"]} className="is-text has-text-black is-underlined" >View Credits</Link>
                                </span>
                            </div>

                            <div className="tabs is-medium" style={{ scrollbarWidth: 'none' }}>
                                <ul>
                                    <li className={activeAIGeneratedTab === "ai-calculator" ? "is-active" : ""}>
                                        <a onClick={() => setActiveAIGeneratedTab("ai-calculator")}>AI Calculator Generator</a>
                                    </li>
                                    <li className={activeAIGeneratedTab === "projects" ? "is-active" : ""}>
                                        <a onClick={() => setActiveAIGeneratedTab("projects")}>Projects</a>
                                    </li>
                                </ul>
                            </div>
                        </>

                    ) : (

                        <>
                            <div className="is-flex is-align-items-center is-justify-content-space-between w-100">
                                <Icon iconName={"arrow-left"} onClick={backToList} style={{ filter: 'opacity(0.5)', cursor: 'pointer' }} />
                                <div className="ai-calculator-header">
                                    <h2 className="has-text-centered">AI Calculator Generator</h2>
                                    <p className={"has-text-dark has-text-centered"}>
                                        Create Calculators for your Audience & Grow your Organic Traffic
                                    </p>
                                </div>
                                <svg width="30" height="24" viewBox="0 0 30 24" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ opacity: '0' }}>
                                    <path d="M26.0435 12.0003H2.82031M2.82031 12.0003L12.8382 1.98242M2.82031 12.0003L12.8382 22.0181" stroke="black" stroke-opacity="0.5" stroke-width="3" />
                                </svg>
                            </div>
                            <hr className="horizontal-line" />
                        </>
                    )}

                    {!calculatorHTML ? (
                        <>
                            {activeAIGeneratedTab === "ai-calculator" &&
                                <div className="is-flex is-align-items-center is-flex-direction-column ai-calculator-form-container">
                                    <h3 >Describe a calculator idea, let AI build it for your site.</h3>
                                    <hr />
                                    <div className="ai-calculator-input-group">
                                        <div className="field">
                                            <label className="is-size-5 has-text-weight-medium has-text-black label">Calculator Name</label>
                                            <div className="control">
                                                <input
                                                    id="calculator-type"
                                                    className="input"
                                                    type="text"
                                                    placeholder="e.g., BMI, mortgage, tip, currency converter"
                                                    value={calculatorType}
                                                    onChange={(e) => setCalculatorType(e.target.value)}
                                                    disabled={isLoading}
                                                />
                                            </div>
                                        </div>
                                        <label className="mt-1 is-size-5 has-text-weight-medium has-text-black label">that does the following:</label>
                                        <textarea
                                            value={calcDescription}
                                            onChange={(e) => setCalcDescription(e.target.value)}
                                            className="textarea textarea-box"
                                            placeholder="(Optional)"
                                            disabled={isLoading}
                                        ></textarea>
                                        <button
                                            className="mt-2 button is-responsive is-link ai-generate-calculator-button is-align-self-flex-start"
                                            onClick={generateCalculator}
                                            disabled={isLoading || (calculatorType.trim() === "")}
                                        >
                                            {isLoading ? "Generating..." :
                                                <>
                                                    Generate <FontAwesomeIcon icon={faArrowRight} className="ml-2 is-size-6" />
                                                </>
                                            }
                                        </button>
                                    </div>
                                </div >
                            }

                            {activeAIGeneratedTab === "projects" &&
                                <AICalculatorTable />
                            }
                        </>

                    ) : (
                        <div className="ai-calculator-result-section">
                            <div className="ai-left-container">
                                <div className="ai-all-sections-container">

                                    {/* Update Calculator Section */}
                                    <div className="ai-section-wrapper">
                                        <div
                                            className="ai-section-header"
                                            onClick={() => toggleSection('update')}
                                        >
                                            <div className={`ai-section-arrow ${expandedSections.update ? 'expanded' : ''}`}>
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                </svg>
                                            </div>
                                            <h3 className="ai-section-title">What changes do you want in the Calculator?</h3>
                                        </div>
                                        {expandedSections.update && (
                                            <div className="ai-section-content">
                                                <div className="ai-calculator-chat-container">
                                                    <div className="ai-content">
                                                        <textarea
                                                            className="textarea custom-textarea"
                                                            placeholder="Add the following...."
                                                            value={userInput}
                                                            onChange={(e) => setUserInput(e.target.value)}
                                                        />
                                                        <button
                                                            className="button is-primary custom-button"
                                                            onClick={requestModification}
                                                            disabled={isLoading || isModifying || (!userInput || !userInput.trim())}
                                                        >
                                                            {isModifying ? "Generating..." : (isLoading ? "Updating..." : "Update Calculator")}
                                                        </button>
                                                        <div className="field mt-6">
                                                            <label className="checkbox">
                                                                <input
                                                                    type="checkbox"
                                                                    checked={isChecked}
                                                                    onChange={() => setIsChecked(!isChecked)}
                                                                    className="mr-2"
                                                                />
                                                                Make the Calculator Name as H1 Tag
                                                            </label>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    {/* Versions Section */}
                                    <div className="ai-section-wrapper">
                                        <div
                                            className="ai-section-header"
                                            onClick={() => toggleSection('versions')}
                                        >
                                            <div className={`ai-section-arrow ${expandedSections.versions ? 'expanded' : ''}`}>
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                </svg>
                                            </div>
                                            <h3 className="ai-section-title">Version History</h3>
                                        </div>
                                        {expandedSections.versions && (
                                            <div className="ai-section-content">
                                                <div className="ai-version-container">
                                                    {versions.length > 0 ? (
                                                        versions.map((version) => (
                                                            <label key={version.id} className="is-flex is-align-items-center mb-3 ml-0" style={{ cursor: "pointer" }}>
                                                                <input
                                                                    type="radio"
                                                                    name="version"
                                                                    value={version.id}
                                                                    checked={selectedVersion === version.id}
                                                                    onChange={() => handleVersionChange(version.id)}
                                                                    className="mr-2 custom-radio"
                                                                />
                                                                <div>
                                                                    <p style={{ fontWeight: '500' }}>{version.version_name}</p>
                                                                    <p className="is-size-7 has-text-grey">{version.created_on}</p>
                                                                </div>
                                                            </label>
                                                        ))
                                                    ) : (
                                                        <p>No versions available for this calculator.</p>
                                                    )}
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    {/* Get Embed Code Section */}
                                    <div className="ai-section-wrapper">
                                        <div
                                            className="ai-section-header"
                                            onClick={() => toggleSection('embed')}
                                        >
                                            <div className={`ai-section-arrow ${expandedSections.embed ? 'expanded' : ''}`}>
                                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M6 9L12 15L18 9" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                </svg>
                                            </div>
                                            <h3 className="ai-section-title">Get Embed Code</h3>
                                        </div>
                                        {expandedSections.embed && (
                                            <div className="ai-section-content">
                                                <div className="ai-embed-container">
                                                    {!embedCode.scriptTag ? (
                                                        <div className="ai-section">
                                                            <p className="subtitle">Generate embed code to integrate your calculator</p>
                                                            <AbunButton
                                                                className="mt-3"
                                                                disabled={generatingEmbedCode}
                                                                type="primary"
                                                                clickHandler={handleGenerateEmbedCode}
                                                            >
                                                                {generatingEmbedCode ? "Generating..." : "Generate Embed Code"}
                                                            </AbunButton>
                                                        </div>
                                                    ) : (
                                                        <>
                                                            <div className="ai-section mb-5">
                                                                <div className="notification is-info is-light">
                                                                    <p className="has-text-weight-semibold mb-2">Integration Instructions:</p>
                                                                    <ul className="ml-4">
                                                                        <li>• Add the script tag to your page's &lt;head&gt; section</li>
                                                                        <li>• Place the div tag wherever you want the calculator to appear on your page</li>
                                                                        <li>• The calculator will automatically load and display inside the div</li>
                                                                        <li>• You can style the div with CSS to match your website's design</li>
                                                                    </ul>
                                                                </div>
                                                            </div>
                                                            <div className="ai-section">
                                                                <h3 className="subtitle">Step 1: Add this script to the &lt;head&gt; tag of your page</h3>
                                                                <textarea
                                                                    className="textarea"
                                                                    value={embedCode.scriptTag}
                                                                    readOnly
                                                                />
                                                                <button
                                                                    className="copy-btn button is-primary"
                                                                    onClick={() => copyToClipboard(embedCode.scriptTag)}
                                                                >
                                                                    Copy Script
                                                                </button>
                                                            </div>

                                                            <div className="ai-section mt-5">
                                                                <h3 className="subtitle">Step 2: Add this div where you want the calculator to appear</h3>
                                                                <p className="mb-3">
                                                                    Place this div tag in your HTML where you want the calculator to be displayed.
                                                                    The calculator will automatically load inside this div.
                                                                </p>
                                                                <textarea
                                                                    className="textarea"
                                                                    value={`<div id="abun-ai-calculator"></div>`}
                                                                    readOnly
                                                                />
                                                                <button
                                                                    className="copy-btn button is-primary"
                                                                    onClick={() => copyToClipboard(`<div id="abun-ai-calculator"></div>`)}
                                                                >
                                                                    Copy Tag
                                                                </button>
                                                            </div>
                                                        </>
                                                    )}
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                </div>
                            </div>

                            <hr className="ai-horizontal-line" />

                            <div className="ai-calculator-preview-section">
                                <BrowserMockup
                                    websiteDomain={basePageData?.active_website_domain || 'example.com'}
                                    calculatorTitle={calculatorType || 'Calculator'}
                                    isLoading={isModifying}
                                >
                                    <div className="ai-preview-container">
                                        <div className="ai-preview-content" ref={previewContainerRef}></div>
                                    </div>
                                </BrowserMockup>
                            </div>
                        </div>
                    )
                    }
                </div >

                <ErrorAlert ref={errorAlertRef} />
                <SuccessAlert ref={successAlertRef} />
            </div >

        </>
    );
};

export default withAdminAndProductionCheck(AICalculatorGenerator);

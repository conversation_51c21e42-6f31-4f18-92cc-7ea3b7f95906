@import "../../assets/themes/mainTheme";
@import "../../assets/fonts/customFonts";

@import "bulma/sass/utilities/all";
@import "bulma/sass/base/all";
@import "bulma/sass/helpers/typography";
@import "bulma/sass/grid/columns";
@import "bulma/sass/elements/container";
@import "bulma/sass/form/_all";
@import "bulma/sass/components/tabs";
@import "bulma/sass/helpers/all";
@import "bulma/sass/elements/all";

@import "../../assets/bulma-overrides/bulmaOverrides";

/* Main container for AI Calculator Generator */
.ai-calculator-generator-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-family: $primary-font !important;
  // max-width: 1500px;
  overflow: visible;
  width: 100%;
  padding: 0; // Remove any default padding
  margin: 0; // Remove any default margin

  /* Responsive adjustments */
  // @media (max-width: 480px) {
  //   height: auto;
  //   width: 100%;
  // }

  // @media (max-width: 769px) {
  //   height: auto;
  //   width: 100%;
  // }

  // @media (min-width: 1024px) {
  //   width: 100vw;
  //   max-width: 100%;
  // }

  /* Header section with back button */
  // .ai-calculator-generator-header {
  //   position: relative;
  //   width: 100%;
  //   height: 50px;

  //   .ai-calculator-back-button {
  //     background: none;
  //     border: none;
  //     cursor: pointer;
  //     display: flex;
  //     align-items: center;
  //     padding: 8px;
  //     border-radius: 4px;
  //     transition: background-color 0.2s;
  //     position: absolute;
  //     left: 0;

  //     &:hover {
  //       background-color: rgba(0, 0, 0, 0.05);
  //     }

  //     svg {
  //       width: 24px;
  //       height: 24px;
  //     }
  //   }
  // }

  /* Content section */
  .ai-calculator-generator-content {
    display: flex;
    flex-direction: column;
    // align-items: center;
    width: 100%;
    padding: 0; // Remove any default padding
    margin: 0; // Remove any default margin

    .ai-calculator-header {

      h2 {
        font-family: $primary-font !important;
        font-size: 2rem !important;
        font-weight: 600 !important;
        margin-bottom: 4px;
      }

      p {
        // color: rgba(0, 0, 0, .698);
        font-family: $secondary-font !important;
        font-size: 1.125rem !important;
      }
    }

    /* Input card for calculator type */
    .ai-calculator-form-container {
      border: 1px solid #e7e7e7;
      border-radius: 8px;
      margin-bottom: 1.5rem;
      width: fit-content;

      @media (max-width: 768px) {
        width: 100%;
        margin-bottom: 1rem;
      }

      h3 {
        font-size: 1.4rem;
        font-weight: 600;
        padding: 1rem;
        align-self: start;

        @media (max-width: 768px) {
          font-size: 1.2rem;
          padding: 0.8rem;
        }

        @media (max-width: 480px) {
          font-size: 1.1rem;
          padding: 0.6rem;
        }
      }

      hr {
        background: #e7e7e7;
        height: 1px;
        margin: 0;
        width: 100%;
      }

      .ai-calculator-input-group {
        justify-items: center;
        display: flex;
        flex-direction: column;
        padding: 1rem;
        padding-left: 1.6rem;
        width: 100%;

        @media (max-width: 768px) {
          padding: 0.8rem;
          padding-left: 1rem;
        }

        @media (max-width: 480px) {
          padding: 0.6rem;
          padding-left: 0.8rem;
        }

        .field {
          width: 100%;

          .input {
            @media (max-width: 480px) {
              font-size: 14px;
            }
          }

          .label {
            @media (max-width: 480px) {
              font-size: 16px;
            }
          }
        }

        .textarea-box {
          height: 170px;
          min-height: 86px;
          margin-bottom: 16px;
          resize: vertical;
          width: 100%;
          max-width: 415px;
          border-radius: 10px;
          padding: 17px;
          font-family: $secondary-font;

          @media (max-width: 768px) {
            height: 140px;
            min-height: 70px;
            max-width: 100%;
            padding: 15px;
          }

          @media (max-width: 480px) {
            height: 120px;
            min-height: 60px;
            padding: 12px;
            font-size: 14px;
          }
        }

        .ai-generate-calculator-button {
          @media (max-width: 480px) {
            width: 100%;
            font-size: 14px;
            padding: 12px;
          }
        }
      }
    }

    /* Results section with chat and preview */
    .ai-calculator-result-section {
      gap: 0; // Remove gap to allow browser mockup to extend to edges
      width: 100%;
      display: flex;
      align-items: stretch; // Make both sides same height
      justify-content: space-between;
      height: calc(100vh - 200px); // Full viewport height minus header space
      min-height: 600px; // Minimum height for smaller screens

      @media (max-width: 1046px) {
        flex-direction: column;
        height: auto; // Let it be auto on mobile
        min-height: auto;
      }

      @media (max-width: 768px) {
        padding: 0 10px;
        min-height: auto;
      }

      @media (max-width: 480px) {
        padding: 0 5px;
        min-height: auto;
      }

      .ai-horizontal-line {
        background-color: rgba(0, 0, 0, 0.1);
        width: 1.6px;
        height: 100%; // Match the full height of the result section
        margin: 0; // Remove all margins
        align-self: stretch; // Stretch to full height

        @media (max-width: 1046px) {
          height: 2px;
          width: 100%;
        }
      }

      /* Chat container */
      .ai-calculator-chat-container {
        margin-left: 0;
        display: flex;

        .ai-content {
          display: flex;
          flex-direction: column;
          gap: 15px;
          flex-grow: 1;
          width: 100%;

          @media (max-width: 768px) {
            gap: 12px;
          }

          @media (max-width: 480px) {
            gap: 10px;
          }

          .title-text {
            font-size: 1.3rem;
            font-weight: 500;
            margin-left: 0;
            color: #333;

            @media (max-width: 768px) {
              font-size: 1.2rem;
            }

            @media (max-width: 480px) {
              font-size: 1.1rem;
            }
          }

          .custom-textarea {
            border-radius: 7px;
            height: 150px;
            min-height: 80px;
            resize: vertical;

            @media (max-width: 768px) {
              height: 130px;
              min-height: 70px;
            }

            @media (max-width: 480px) {
              height: 110px;
              min-height: 60px;
              font-size: 14px;
            }
          }

          .custom-button {
            width: fit-content;

            @media (max-width: 480px) {
              width: 100%;
              font-size: 14px;
              padding: 10px;
            }
          }

          input[type="checkbox"] {
            accent-color: #000000;
          }

          .field {
            @media (max-width: 480px) {
              margin-top: 1rem;
            }

            .checkbox {
              @media (max-width: 480px) {
                font-size: 14px;
              }
            }
          }
        }
      }

      /* Embed container */
      .ai-embed-container {
        padding: 0;

        .ai-section {
          display: flex;
          flex-direction: column;
          gap: 12px;
          position: relative;

          @media (max-width: 768px) {
            gap: 10px;
          }

          @media (max-width: 480px) {
            gap: 8px;
          }

          .textarea {
            border-radius: 10px;
            resize: vertical;
            font-family: monospace;
            word-break: break-all;
            overflow-wrap: break-word;

            @media (max-width: 768px) {
              font-size: 13px;
              min-height: 90px;
              padding: 12px;
            }

            @media (max-width: 480px) {
              font-size: 12px;
              min-height: 80px;
              padding: 10px;
              line-height: 1.4;
            }
          }

          .subtitle {
            font-size: 1.2rem;
            margin-left: 7px;
            font-weight: 600;
            margin-bottom: 0 !important;

            @media (max-width: 768px) {
              font-size: 1.1rem;
              margin-left: 5px;
            }

            @media (max-width: 480px) {
              font-size: 1rem;
              margin-left: 0;
            }
          }

          .copy-btn {
            width: 19%;
            height: 36px;
            font-size: 18px;
            border-radius: 8px;
            margin-left: auto;
            min-width: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
            transition: all 0.2s ease;

            @media (max-width: 768px) {
              width: 30%;
              height: 38px;
              font-size: 16px;
              min-width: 110px;
            }

            @media (max-width: 480px) {
              width: 100%;
              height: 42px;
              font-size: 15px;
              margin-left: 0;
              margin-top: 8px;
              min-width: auto;
              padding: 10px 15px;
            }

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 2px 8px rgba(46, 100, 254, 0.3);
            }

            &:active {
              transform: translateY(0);
            }
          }

          .notification {
            @media (max-width: 768px) {
              font-size: 15px;
              padding: 18px;

              .has-text-weight-semibold {
                font-size: 16px;
                margin-bottom: 8px;
              }

              ul {
                margin-left: 20px;

                li {
                  font-size: 14px;
                  margin-bottom: 6px;
                  line-height: 1.4;
                }
              }
            }

            @media (max-width: 480px) {
              font-size: 14px;
              padding: 15px;

              .has-text-weight-semibold {
                font-size: 15px;
                margin-bottom: 6px;
              }

              ul {
                margin-left: 15px;

                li {
                  font-size: 13px;
                  margin-bottom: 5px;
                  line-height: 1.3;
                }
              }
            }
          }
        }
      }

      /* version container */
      .ai-version-container {
        margin-left: 0;

        label {
          @media (max-width: 480px) {
            font-size: 14px;
            margin-bottom: 15px;
          }

          div {
            p {
              @media (max-width: 480px) {
                font-size: 14px;
                margin-bottom: 2px;
              }

              &.is-size-7 {
                @media (max-width: 480px) {
                  font-size: 12px;
                }
              }
            }
          }
        }

        .custom-radio {
          margin-bottom: 0.8rem;
          appearance: none;
          width: 16px;
          height: 16px;
          border: 2px solid black;
          border-radius: 50%;
          position: relative;
          cursor: pointer;
          outline: none;

          @media (max-width: 480px) {
            width: 14px;
            height: 14px;
            margin-bottom: 0.6rem;
          }
        }

        .custom-radio:checked::before {
          content: "";
          width: 8px;
          height: 8px;
          background-color: black;
          border-radius: 50%;
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);

          @media (max-width: 480px) {
            width: 6px;
            height: 6px;
          }
        }
      }

      /* Preview section */
      .ai-calculator-preview-section {
        display: flex;
        flex-direction: column;
        flex: 1; // Take up all remaining space after left section and line
        height: 100%; // Fill the full height of the result section
        margin: 0; // Remove all margins
        padding: 0; // Remove all padding
        box-sizing: border-box; // Ensure proper box model

        @media (max-width: 1046px) {
          width: 100%;
          margin-top: 0;
          flex: none;
          height: 60vh;
          min-height: 400px;
        }

        @media (max-width: 768px) {
          height: 50vh;
          min-height: 350px;
        }

        @media (max-width: 480px) {
          height: 45vh;
          min-height: 300px;
        }
      }

      .ai-left-container {
        overflow-y: auto;
        margin-top: 0; // Remove margin to align with right section
        flex: 0 0 38%;
        height: 100%; // Fill the full height
        max-height: calc(100vh - 250px); // Prevent content from being cut off on larger screens

        // Ensure smooth scrolling
        scroll-behavior: smooth;

        // Custom scrollbar styling for better UX
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }

        @media (max-width: 1046px) {
          width: 100%;
          height: auto;
          max-height: 70vh;
          overflow-y: auto;
        }

        @media (max-width: 768px) {
          max-height: 60vh;
          padding: 0 5px;
        }

        @media (max-width: 480px) {
          max-height: 50vh;
          padding: 0;
        }

        // Container for all sections displayed simultaneously
        .ai-all-sections-container {
          display: flex;
          flex-direction: column;
          gap: 30px;
          padding: 20px 0;

          @media (max-width: 768px) {
            gap: 25px;
            padding: 15px 0;
          }

          @media (max-width: 480px) {
            gap: 20px;
            padding: 10px 0;
          }
        }

        // Individual section wrapper
        .ai-section-wrapper {
          border: 1px solid #e7e7e7;
          border-radius: 12px;
          padding: 20px;
          background-color: #ffffff;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          transition: box-shadow 0.2s ease;

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
          }

          @media (max-width: 768px) {
            padding: 15px;
            border-radius: 10px;
          }

          @media (max-width: 480px) {
            padding: 12px;
            border-radius: 8px;
          }
        }

        // Section header (clickable)
        .ai-section-header {
          display: flex;
          align-items: center;
          cursor: pointer;
          padding: 15px 0;
          border-bottom: 1px solid #e7e7e7;
          transition: all 0.2s ease;

          &:hover {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 15px 10px;
          }

          .ai-section-arrow {
            color: #666;
            transition: transform 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            width: 20px;
            height: 20px;
            flex-shrink: 0;

            &.expanded {
              transform: rotate(180deg);
            }

            svg {
              width: 20px;
              height: 20px;
            }
          }

          .ai-section-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #4a90e2;
            margin: 0;
            flex: 1;
            font-family: $primary-font;
            line-height: 1.2;
            display: flex;
            align-items: center;

            @media (max-width: 768px) {
              font-size: 1.1rem;
            }

            @media (max-width: 480px) {
              font-size: 1rem;
            }
          }

          .ai-section-badge {
            display: flex;
            align-items: center;
            margin-right: 10px;

            .version-count {
              background-color: #4a90e2;
              color: white;
              border-radius: 50%;
              width: 24px;
              height: 24px;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 0.8rem;
              font-weight: 600;
            }
          }
        }

        // Section content (collapsible)
        .ai-section-content {
          padding-top: 20px;
          animation: slideDown 0.3s ease-out;
        }

        @keyframes slideDown {
          from {
            opacity: 0;
            transform: translateY(-10px);
          }

          to {
            opacity: 1;
            transform: translateY(0);
          }
        }


      }
    }

    .copy-button {
      position: absolute;
      right: 0px;
      top: -12px;
      background-color: #cfd6dd;
      color: #000;
      border: none;
      border-radius: 4px;
      padding: 5px 10px;
      cursor: pointer;
      font-size: 0.6rem;
      transition: background-color 0.3s;
      margin-left: auto; // Align to the right

      &:hover {
        background-color: darken(#cfd6dd, 5%);
      }
    }
  }

  .publish-calculator-modal {
    .modal-content {
      padding: 20px;
      border-radius: 8px;
      background-color: #fff;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    }

    input[type="text"] {
      width: 100%;
      padding: 10px;
      margin-bottom: 20px;
      border: 1px solid #ccc;
      border-radius: 4px;
      box-sizing: border-box;

      &:focus {
        border-color: #0056b3;
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 86, 179, 0.25);
      }
    }

    .abun-button {
      display: block;
      width: 100%;
      padding: 10px;
      font-size: 16px;
      text-align: center;
      color: #fff;
      background-color: #007bff;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.3s;

      &:hover {
        background-color: #0056b3;
      }

      &:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }
    }

    .verification-results {
      margin-top: 20px;
      padding: 10px;
      border-radius: 4px;
      background-color: #f8f9fa;
      border-left: 5px solid;

      &.success {
        border-color: #28a745;
        color: #28a745;
      }

      &.error {
        border-color: #dc3545;
        color: #dc3545;
      }
    }

    code {
      display: block;
      padding: 10px;
      margin-top: 10px;
      background-color: #f4f4f4;
      border-radius: 4px;
      font-family: $secondary-font;
      white-space: pre-wrap;
    }
  }

  .horizontal-line {
    width: 100%;
    height: 1px;
    margin-bottom: 0;
    background-color: #dedbdb;
  }

  /* Browser Mockup Styles */
  .browser-mockup {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    background-color: #ffffff;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    flex: 1; // Take up all available space in the preview section
    margin: 0; // Remove any default margins
    padding: 0; // Remove any default padding

    .browser-header {
      display: flex;
      align-items: center;
      background: linear-gradient(135deg, #f5f5f5 0%, #e8e8e8 100%);
      padding: 12px 16px;
      border-bottom: 1px solid #d0d0d0;
      min-height: 44px;

      .browser-controls {
        display: flex;
        gap: 8px;
        margin-right: 16px;

        .browser-control {
          width: 12px;
          height: 12px;
          border-radius: 50%;

          &.close {
            background-color: #ff5f57;
            border: 1px solid #e0443e;
          }

          &.minimize {
            background-color: #ffbd2e;
            border: 1px solid #dea123;
          }

          &.maximize {
            background-color: #28ca42;
            border: 1px solid #1aab29;
          }
        }
      }

      .browser-address-bar {
        flex: 1;
        background-color: #ffffff;
        border: 1px solid #d0d0d0;
        border-radius: 6px;
        padding: 6px 12px;
        font-family: $secondary-font;

        .browser-url {
          color: #666666;
          font-size: 14px;
          font-weight: 400;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
          display: block;
        }
      }
    }

    .browser-notification-bar {
      background: #E6EBEE;
      border-bottom: 1px solid #d0d0d0;
      padding: 8px 16px;
      color: #000;
      font-size: 13px;

      .notification-content {
        display: flex;
        align-items: center;
        justify-content: center;

        .notification-text {
          font-weight: 500;
          color: #000;
        }
      }
    }

    .browser-content {
      background-color: #ffffff;
      flex: 1;
      display: flex;
      flex-direction: column;
      position: relative;

      .ai-preview-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #eee;
        background-color: #fafafa;

        h3 {
          font-family: $primary-font;
          font-size: 1.25rem;
          margin: 0;
          color: #333;
        }

        .ai-preview-actions {
          display: flex;
          gap: 8px;

          .ai-action-button {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            border: none;
            background-color: #f0f0f0;
            color: $grey-darker;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.875rem;
            font-family: $secondary-font;
            transition: background-color 0.2s;

            &:hover {
              background-color: #e0e0e0;
            }

            svg {
              width: 16px;
              height: 16px;
            }
          }
        }
      }

      .ai-preview-container {
        padding: 0;
        flex: 1;
        overflow: hidden; // Remove scroll, let content fill the space
        border: none;
        display: flex;
        flex-direction: column;
        height: 100%;

        .ai-preview-content {
          width: 100%;
          height: 100%;
          flex: 1;
          overflow: hidden; // Prevent internal scrolling
        }
      }
    }

    /* Loading overlay for browser mockup */
    .browser-loading-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(255, 255, 255, 0.95);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
      backdrop-filter: blur(2px);

      .browser-loading-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 16px;

        .browser-loading-spinner {
          width: 40px;
          height: 40px;
          border: 3px solid #f3f3f3;
          border-top: 3px solid #2e64fe;
          border-radius: 50%;
          animation: browser-spin 1s linear infinite;
        }

        .browser-loading-text {
          color: #666;
          font-size: 16px;
          font-weight: 500;
          font-family: $primary-font;
          margin: 0;
        }
      }
    }

    @keyframes browser-spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    @media (max-width: 768px) {
      .browser-header {
        padding: 8px 12px;
        min-height: 36px;

        .browser-controls {
          gap: 6px;
          margin-right: 12px;

          .browser-control {
            width: 10px;
            height: 10px;
          }
        }

        .browser-address-bar {
          padding: 4px 8px;

          .browser-url {
            font-size: 12px;
          }
        }
      }

      .browser-notification-bar {
        padding: 6px 12px;
        font-size: 12px;
      }
    }

    @media (max-width: 480px) {
      .browser-header {
        padding: 6px 8px;
        min-height: 32px;

        .browser-controls {
          gap: 4px;
          margin-right: 8px;

          .browser-control {
            width: 8px;
            height: 8px;
          }
        }

        .browser-address-bar {
          padding: 3px 6px;

          .browser-url {
            font-size: 11px;
          }
        }
      }

      .browser-notification-bar {
        padding: 4px 8px;
        font-size: 11px;
      }

      .browser-loading-overlay {
        .browser-loading-content {
          gap: 12px;

          .browser-loading-spinner {
            width: 30px;
            height: 30px;
            border-width: 2px;
          }

          .browser-loading-text {
            font-size: 14px;
          }
        }
      }
    }
  }
}